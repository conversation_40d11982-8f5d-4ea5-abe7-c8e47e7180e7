import { Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import HomePage from './pages/HomePage';
import PracticePage from './pages/PracticePage';
import TutorialPage from './pages/TutorialPage';
import AlgorithmTrainerPage from './pages/AlgorithmTrainerPage';
import StatsPage from './pages/StatsPage';
import SettingsPage from './pages/SettingsPage';

function App() {
  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route index element={<HomePage />} />
        <Route path="practice" element={<PracticePage />} />
        <Route path="tutorial" element={<TutorialPage />} />
        <Route path="trainer" element={<AlgorithmTrainerPage />} />
        <Route path="stats" element={<StatsPage />} />
        <Route path="settings" element={<SettingsPage />} />
      </Route>
    </Routes>
  );
}

export default App;
