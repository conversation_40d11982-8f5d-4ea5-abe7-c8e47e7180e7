export interface TutorialStep {
  title: string;
  description: string;
  algorithm: string;
  highlightedPieces?: {
    positions?: [number, number, number][];
    faces?: string[];
  };
}

export const rouxMethod: TutorialStep[] = [
  {
    title: 'Step 1: First Block',
    description:
      'The first step of the Roux method is to build a 1x2x3 block on the left side of the cube. This block consists of a center, three edges, and three corners, all solved relative to each other.',
    algorithm: '',
    highlightedPieces: {
      positions: [
        [-1, 1, 1],
        [-1, 1, 0],
        [-1, 1, -1],
        [-1, 0, 1],
        [-1, 0, 0],
        [-1, 0, -1],
        [-1, -1, 1],
        [-1, -1, 0],
        [-1, -1, -1],
      ],
    },
  },
  {
    title: 'Step 2: Second Block',
    description:
      'The second step is to build another 1x2x3 block on the right side of the cube, opposite the first block. This step is usually performed without rotating the cube.',
    algorithm: '',
    highlightedPieces: {
      positions: [
        [1, 1, 1],
        [1, 1, 0],
        [1, 1, -1],
        [1, 0, 1],
        [1, 0, 0],
        [1, 0, -1],
        [1, -1, 1],
        [1, -1, 0],
        [1, -1, -1],
      ],
    },
  },
  {
    title: 'Step 3: Corners of the Last Layer (CMLL)',
    description:
      "The third step is to solve the corners of the last layer. This step orients and permutes the four top-layer corners while preserving the two blocks you've already built. There are 42 different algorithms for this step. Here is a common one.",
    algorithm: "R U R' U' R' F R F'",
    highlightedPieces: {
      positions: [
        [1, 1, 1],
        [1, 1, -1],
        [-1, 1, 1],
        [-1, 1, -1],
      ],
    },
  },
  {
    title: 'Step 4: Last Six Edges (LSE)',
    description:
      "The final step is to solve the last six edges. This is done using only moves of the U-layer and the M-slice (the middle layer). This step is highly intuitive and there are fewer algorithms to learn.",
    algorithm: 'M U M',
    highlightedPieces: {
      positions: [
        [1, 1, 0],
        [-1, 1, 0],
        [0, 1, 1],
        [0, 1, -1],
        [0, -1, 1],
        [0, -1, -1],
      ],
    },
  },
]; 