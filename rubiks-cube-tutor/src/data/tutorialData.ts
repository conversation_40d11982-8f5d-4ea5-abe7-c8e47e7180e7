export interface TutorialStep {
  title: string;
  description: string;
  algorithm: string;
  highlightedPieces?: {
    positions?: [number, number, number][];
    faces?: string[];
  };
}

export const beginnerMethod: TutorialStep[] = [
  {
    title: 'Step 1: The White Cross',
    description:
      "The first step is to create a white cross on the top face of the cube. Make sure the colors of the edge pieces match the colors of the center pieces on the sides.",
    algorithm: '',
    highlightedPieces: {
      positions: [
        [1, 1, 0],
        [-1, 1, 0],
        [0, 1, 1],
        [0, 1, -1],
      ],
    },
  },
  {
    title: 'Step 2: Solve the White Corners',
    description:
      "Now, solve the four white corner pieces to complete the first layer. Find a white corner piece in the bottom layer and use the algorithm to move it to the correct position.",
    algorithm: "R' D' R D",
    highlightedPieces: {
        positions: [
            [1, 1, 1],
            [1, 1, -1],
            [-1, 1, 1],
            [-1, 1, -1],
        ]
    }
  },
  {
    title: 'Step 3: Solve the Middle Layer',
    description:
      "Next, solve the four edge pieces in the middle layer. Find an edge piece in the bottom layer that does not have yellow on it. Move it to the correct position using one of the two algorithms.",
    algorithm: 'U R U\' R\' U\' F\' U F or U\' L\' U L U F U\' F\'',
  },
  {
    title: 'Step 4: The Yellow Cross',
    description:
      "Create a yellow cross on the top face. You may see a dot, a line, or an 'L' shape. Use the algorithm to cycle through these shapes until you get a cross.",
    algorithm: 'F R U R\' U\' F\'',
  },
  {
    title: 'Step 5: Position the Yellow Edges',
    description:
      'After creating the yellow cross, you need to make sure the yellow edge pieces are in their correct positions. Use the algorithm to swap adjacent or opposite edges until they all match the side center colors.',
    algorithm: "R U R' U R U U R'",
  },
  {
    title: 'Step 6: Position the Yellow Corners',
    description:
      "The next step is to get the yellow corners in their correct positions, even if they are not oriented correctly. Find a corner that is in the right spot and use the algorithm to cycle the other three.",
    algorithm: "U R U' L' U R' U' L",
  },
  {
    title: 'Step 7: Orient the Yellow Corners',
    description:
      'Finally, orient the yellow corners to solve the cube. This may seem like it is messing up the cube, but trust the algorithm! Repeat it until the yellow sticker is on top, then move the top layer to the next unsolved corner and repeat.',
    algorithm: "R' D' R D",
  },
]; 