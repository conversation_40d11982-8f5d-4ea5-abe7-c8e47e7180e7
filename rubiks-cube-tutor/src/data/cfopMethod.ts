export interface TutorialStep {
  title: string;
  description: string;
  algorithm: string;
  highlightedPieces?: {
    positions?: [number, number, number][];
    faces?: string[];
  };
}

export const cfopMethod: TutorialStep[] = [
  {
    title: 'Step 1: The Cross',
    description:
      "The first step of the CFOP method is to solve a cross on the bottom layer. This is similar to the beginner's method, but it is usually done on the bottom and should be done more efficiently.",
    algorithm: '',
    highlightedPieces: {
      positions: [
        [1, -1, 0],
        [-1, -1, 0],
        [0, -1, 1],
        [0, -1, -1],
      ],
    },
  },
  {
    title: 'Step 2: First Two Layers (F2L)',
    description:
      "The second step is to solve the first two layers simultaneously. This is done by pairing up a corner and an edge piece from the top layer and inserting them into their correct slot in the first two layers.",
    algorithm: 'Various algorithms depending on the case.',
    highlightedPieces: {
        positions: [
            [1, -1, 1],
            [1, 0, 1],
            [1, -1, -1],
            [1, 0, -1],
            [-1, -1, 1],
            [-1, 0, 1],
            [-1, -1, -1],
            [-1, 0, -1],
        ]
    }
  },
  {
    title: 'Step 3: Orient Last Layer (OLL)',
    description:
      "The third step is to orient the last layer so that all the pieces on the top face are yellow. There are 57 different algorithms for this step. Here is one of the most common cases, the 'Sune'.",
    algorithm: "R U R' U R U2 R'",
    highlightedPieces: {
      positions: [
        [1, 1, 1],
        [1, 1, 0],
        [1, 1, -1],
        [-1, 1, 1],
        [-1, 1, 0],
        [-1, 1, -1],
        [0, 1, 1],
        [0, 1, -1],
      ],
    },
  },
  {
    title: 'Step 4: Permute Last Layer (PLL)',
    description:
      "The final step is to permute the last layer, which means moving the pieces to their final positions. There are 21 different algorithms for this step. Here is a 'U-Perm' to swap two adjacent corners and two adjacent edges.",
    algorithm: "R2 U R U R' U' R' U' R' U R'",
    highlightedPieces: {
      positions: [
        [1, 1, 1],
        [1, 1, -1],
        [-1, 1, 1],
        [-1, 1, -1],
        [1, 1, 0],
        [-1, 1, 0],
        [0, 1, 1],
        [0, 1, -1],
      ],
    },
  },
]; 