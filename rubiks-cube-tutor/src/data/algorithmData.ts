export interface Algorithm {
  name: string;
  sequence: string;
}

export const ollAlgorithms: Algorithm[] = [
  { name: 'OLL 21 (H)', sequence: "R U R' U R U' R' U R U2 R'" },
  { name: 'OLL 22 (Pi)', sequence: "R U2 R2 U' R2 U' R2 U2 R" },
  { name: 'OLL 23 (T)', sequence: "R2 U' R' U' R U R U R U' R" },
  { name: 'OLL 24 (L)', sequence: "r U R' U' r' F R F'" },
  { name: 'OLL 25 (Sune)', sequence: "R U R' U R U2 R'" },
  { name: 'OLL 26 (Anti-Sune)', sequence: "R' U' R U' R' U2 R" },
  { name: 'OLL 27 (U)', sequence: 'R U R2 U\' R\' F R U R U\' F\'' },
];

export const pllAlgorithms: Algorithm[] = [
  { name: 'PLL Aa', sequence: "x L2 D2 L' U' L D2 L' U L'" },
  { name: 'PLL Ab', sequence: "x' L2 D2 L U L' D2 L U' L" },
  { name: 'PLL F', sequence: "R' U' F' R U R' U' R' F R2 U' R' U' R U R' U R" },
  { name: 'PLL Ga', sequence: "R2 U R' U R' U' R U' R2 D U' R' U R D'" },
  { name: 'PLL Gb', sequence: "R' U' R U D' R2 U R' U R U' R U' R2 D" },
  { name: 'PLL Gc', sequence: "R2 U' R U' R U R' U R2 D' U R U' R' D" },
  { name: 'PLL Gd', sequence: "R U R' U' D R2 U' R U' R' U R' U R2 D'" },
  { name: 'PLL H', sequence: "M2' U M2' U2 M2' U M2'" },
  { name: 'PLL Ua', sequence: "M2' U M' U2 M U M2'" },
  { name: 'PLL Ub', sequence: "M2' U' M' U2 M U' M2'" },
  { name: 'PLL Z', sequence: "M' U' M2' U' M2' U' M' U2 M2'" },
];
