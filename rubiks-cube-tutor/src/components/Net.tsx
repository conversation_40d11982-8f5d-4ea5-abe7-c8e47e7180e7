import React from 'react';
import useCubeStore from '../store/cubeStore';
import { faceNormals } from '../utils/cubeUtils';
import './Net.css';

const Net: React.FC = () => {
  const { cubies, stickers } = useCubeStore();

  const getFaceColors = (face: keyof typeof faceNormals) => {
    const faceNormal = faceNormals[face];
    const onFaceCubies = cubies.filter((c) => Math.round(c.position.dot(faceNormal)) === 1);

    const sortedCubies = onFaceCubies.sort((a, b) => {
        if (face === 'top' || face === 'bottom') {
            return a.position.z - b.position.z || a.position.x - b.position.x;
        }
        if (face === 'front' || face === 'back') {
            return b.position.y - a.position.y || a.position.x - b.position.x;
        }
        // left or right
        return b.position.y - a.position.y || (face === 'left' ? b.position.z : a.position.z) - (face === 'left' ? a.position.z : b.position.z);
    });
    
    return sortedCubies.map((cubie) => {
      const sticker = stickers.find(s => s.cubieId === cubie.id && s.face === face);
      return sticker ? sticker.color : 'grey';
    });
  };

  const faces = {
    up: getFaceColors('top'),
    down: getFaceColors('bottom'),
    left: getFaceColors('left'),
    front: getFaceColors('front'),
    right: getFaceColors('right'),
    back: getFaceColors('back'),
  };

  return (
    <div className="net">
      <div className="net-row">
        <div className="net-face-placeholder"></div>
        <div className="net-face up">{faces.up.map((c, i) => <div key={i} className="sticker" style={{ background: c }}></div>)}</div>
      </div>
      <div className="net-row">
        <div className="net-face left">{faces.left.map((c, i) => <div key={i} className="sticker" style={{ background: c }}></div>)}</div>
        <div className="net-face front">{faces.front.map((c, i) => <div key={i} className="sticker" style={{ background: c }}></div>)}</div>
        <div className="net-face right">{faces.right.map((c, i) => <div key={i} className="sticker" style={{ background: c }}></div>)}</div>
        <div className="net-face back">{faces.back.map((c, i) => <div key={i} className="sticker" style={{ background: c }}></div>)}</div>
      </div>
      <div className="net-row">
        <div className="net-face-placeholder"></div>
        <div className="net-face down">{faces.down.map((c, i) => <div key={i} className="sticker" style={{ background: c }}></div>)}</div>
      </div>
    </div>
  );
};

export default Net; 