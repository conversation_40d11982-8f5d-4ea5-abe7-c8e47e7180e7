import React from 'react';
import useCubeStore from '../store/cubeStore';
import useTutorialStore from '../store/tutorialStore';
import './Controls.css';

const Controls: React.FC = () => {
  const { rotate, scramble, reset, getHint } = useCubeStore();
  const { tutorial, currentStep } = useTutorialStore();

  const handleHint = () => {
    const hint = getHint(tutorial[currentStep]);
    alert(hint);
  };

  return (
    <div className="controls">
      <div className="control-group">
        <h3>Actions</h3>
        <button onClick={scramble}>Scramble</button>
        <button onClick={reset}>Reset</button>
        <button onClick={handleHint}>Hint</button>
      </div>
      <div className="control-group">
        <h3>Front/Back</h3>
        <button onClick={() => rotate('z', 1, 1)}>F</button>
        <button onClick={() => rotate('z', 1, -1)}>F'</button>
        <button onClick={() => rotate('z', -1, -1)}>B</button>
        <button onClick={() => rotate('z', -1, 1)}>B'</button>
      </div>
      <div className="control-group">
        <h3>Up/Down</h3>
        <button onClick={() => rotate('y', 1, 1)}>U</button>
        <button onClick={() => rotate('y', 1, -1)}>U'</button>
        <button onClick={() => rotate('y', -1, -1)}>D</button>
        <button onClick={() => rotate('y', -1, 1)}>D'</button>
      </div>
      <div className="control-group">
        <h3>Left/Right</h3>
        <button onClick={() => rotate('x', -1, 1)}>L</button>
        <button onClick={() => rotate('x', -1, -1)}>L'</button>
        <button onClick={() => rotate('x', 1, -1)}>R</button>
        <button onClick={() => rotate('x', 1, 1)}>R'</button>
      </div>
    </div>
  );
};

export default Controls; 