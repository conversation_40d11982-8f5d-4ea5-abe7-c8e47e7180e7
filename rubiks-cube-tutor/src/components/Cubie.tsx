import React from 'react';
import { a } from '@react-spring/three';
import { Box } from '@react-three/drei';
import * as THREE from 'three';
import { type ThreeEvent } from '@react-three/fiber';

interface CubieProps {
  position: THREE.Vector3;
  quaternion: any; // react-spring animated value
  colors: string[];
  highlight: boolean;
  onPointerDown: (e: ThreeEvent<PointerEvent>, normal: THREE.Vector3) => void;
  onPointerMove: () => void;
  onPointerUp: (e: ThreeEvent<PointerEvent>) => void;
}

const Cubie: React.FC<CubieProps> = ({
  position,
  quaternion,
  colors,
  highlight,
  onPointerDown,
  onPointerMove,
  onPointerUp,
}) => {
  return (
    <a.group position={position} quaternion={quaternion}>
      <Box args={[1, 1, 1]}>
        {colors.map((color, index) => (
          <mesh
            key={index}
            onPointerDown={(e) =>
              onPointerDown(
                e,
                [
                  new THREE.Vector3(1, 0, 0),
                  new THREE.Vector3(-1, 0, 0),
                  new THREE.Vector3(0, 1, 0),
                  new THREE.Vector3(0, -1, 0),
                  new THREE.Vector3(0, 0, 1),
                  new THREE.Vector3(0, 0, -1),
                ][index]
              )
            }
            onPointerMove={onPointerMove}
            onPointerUp={onPointerUp}
          >
            <planeGeometry args={[1, 1]} />
            <meshStandardMaterial
              attach="material"
              color={color}
              emissive={highlight ? 'yellow' : 'black'}
              emissiveIntensity={highlight ? 0.5 : 0}
              side={THREE.DoubleSide}
            />
          </mesh>
        ))}
      </Box>
    </a.group>
  );
};

export default Cubie; 