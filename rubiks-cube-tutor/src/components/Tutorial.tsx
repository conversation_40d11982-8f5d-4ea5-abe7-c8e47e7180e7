import React, { useEffect } from 'react';
import useCubeStore from '../store/cubeStore';
import useProgressStore from '../store/progressStore';
import useTutorialStore from '../store/tutorialStore';
import './Tutorial.css';

const Tutorial: React.FC = () => {
  const { tutorial, currentStep, nextStep, prevStep } = useTutorialStore();
  const { setHighlightedCubies } = useCubeStore();
  const { addCompletedTutorial } = useProgressStore();
  const step = tutorial[currentStep];

  useEffect(() => {
    const cubieIdsToHighlight =
      step.highlightedPieces?.positions?.map(([x, y, z]) => `${x}-${y}-${z}`) || [];
    setHighlightedCubies(cubieIdsToHighlight);

    return () => {
      setHighlightedCubies([]);
    };
  }, [currentStep, step, setHighlightedCubies]);

  const handleNextStep = () => {
    if (currentStep < tutorial.length - 1) {
      nextStep();
    } else {
      addCompletedTutorial(step.title);
    }
  };

  return (
    <div className="tutorial">
      <h2>{step.title}</h2>
      <p>{step.description}</p>
      {step.algorithm && (
        <div className="algorithm">
          <strong>Algorithm:</strong> <code>{step.algorithm}</code>
        </div>
      )}
      <div className="navigation">
        <button onClick={prevStep} disabled={currentStep === 0}>
          Previous
        </button>
        <button onClick={handleNextStep} disabled={currentStep === tutorial.length - 1}>
          Next
        </button>
      </div>
    </div>
  );
};

export default Tutorial; 