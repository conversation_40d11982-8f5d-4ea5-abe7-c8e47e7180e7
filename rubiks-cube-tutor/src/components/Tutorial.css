.tutorial {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 10px;
  width: 300px;
  color: white;
  text-align: left;
}

.tutorial h2 {
  margin-top: 0;
}

.algorithm {
  margin: 15px 0;
  background: #222;
  padding: 10px;
  border-radius: 5px;
}

.algorithm code {
  font-family: 'Courier New', Courier, monospace;
}

.navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.navigation button {
  background: #444;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
}

.navigation button:hover:not(:disabled) {
  background: #555;
}

.navigation button:disabled {
  background: #333;
  cursor: not-allowed;
}
