import React, { useEffect, useRef, useState } from 'react';
import { useThree, type ThreeEvent } from '@react-three/fiber';
import { useSprings } from '@react-spring/three';
import useCubeStore from '../store/cubeStore';
import useSettingsStore from '../store/settingsStore';
import Cubie from './Cubie';
import * as THREE from 'three';

const Cube: React.FC = () => {
  const { cubies, init, onAnimationComplete, isAnimating, highlightedCubies, rotate } = useCubeStore();
  const { colors } = useSettingsStore();
  const { camera } = useThree();
  const groupRef = useRef<THREE.Group>(null!);

  const [dragging, setDragging] = useState(false);
  const [startPoint, setStartPoint] = useState<THREE.Vector3 | null>(null);
  const [startNormal, setStartNormal] = useState<THREE.Vector3 | null>(null);
  const [startCubiePosition, setStartCubiePosition] = useState<THREE.Vector3 | null>(null);

  useEffect(() => {
    init();
  }, [init]);

  const [springs, api] = useSprings(cubies.length, () => ({
    quaternion: [0, 0, 0, 1],
    config: { tension: 300, friction: 30 },
  }));

  useEffect(() => {
    if (isAnimating) {
      api.start((i) => ({
        quaternion: cubies[i].quaternion.toArray(),
        onRest: i === 0 ? onAnimationComplete : undefined,
      }));
    }
  }, [isAnimating, cubies, api, onAnimationComplete]);

  const handlePointerDown = (e: ThreeEvent<PointerEvent>, normal: THREE.Vector3, cubiePosition: THREE.Vector3) => {
    e.stopPropagation();
    setDragging(true);
    setStartPoint(e.point);
    setStartNormal(normal);
    setStartCubiePosition(cubiePosition);
    groupRef.current.userData.dragging = true;
  };

  const handlePointerMove = () => {
    if (dragging) {
      // Potentially implement visual feedback during drag
    }
  };

  const handlePointerUp = (e: ThreeEvent<PointerEvent>) => {
    if (dragging) {
      setDragging(false);
      groupRef.current.userData.dragging = false;

      if (startPoint && startNormal && startCubiePosition) {
        const endPoint = e.point;
        const dragVector = endPoint.clone().sub(startPoint);

        if (dragVector.length() > 0.5) {
          const screenDragVector = new THREE.Vector2(e.clientX - startPoint.x, e.clientY - startPoint.y);
          const angle = screenDragVector.angle();

          const cameraDirection = new THREE.Vector3();
          camera.getWorldDirection(cameraDirection);

          // const up = new THREE.Vector3(0, 1, 0);
          // const right = new THREE.Vector3().crossVectors(cameraDirection, up).normalize();
      
          let axis: 'x' | 'y' | 'z' | undefined;
          let direction = 1;

          if (angle > -Math.PI / 4 && angle <= Math.PI / 4) { // Right
            axis = 'y';
            direction = -1;
          } else if (angle > Math.PI / 4 && angle <= 3 * Math.PI / 4) { // Up
            axis = 'x';
          } else if (angle > 3 * Math.PI / 4 || angle <= -3 * Math.PI / 4) { // Left
            axis = 'y';
          } else { // Down
            axis = 'x';
            direction = -1;
          }
      
          if (axis) {
            const layer = Math.round(startCubiePosition[axis]);
            rotate(axis, layer, direction);
          }
        }
      }
    }
  };

  return (
    <group ref={groupRef}>
      {springs.map((props, i) => {
        const cubie = cubies[i];
        if (!cubie) return null;

        const { x, y, z } = cubie.position;
        const cubieColors = [
          x === 1 ? colors.right : 'black',
          x === -1 ? colors.left : 'black',
          y === 1 ? colors.top : 'black',
          y === -1 ? colors.bottom : 'black',
          z === 1 ? colors.front : 'black',
          z === -1 ? colors.back : 'black',
        ];

        return (
          <Cubie
            key={cubie.id}
            position={cubie.position.clone().multiplyScalar(1.05)}
            quaternion={props.quaternion}
            colors={cubieColors}
            highlight={highlightedCubies.includes(cubie.id)}
            onPointerDown={(e, normal) => handlePointerDown(e, normal, cubie.position)}
            onPointerMove={handlePointerMove}
            onPointerUp={handlePointerUp}
          />
        );
      })}
    </group>
  );
};

export default Cube;
