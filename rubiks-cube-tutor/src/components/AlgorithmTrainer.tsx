import React, { useState } from 'react';
import useCubeStore from '../store/cubeStore';
import { ollAlgorithms, pllAlgorithms } from '../data/algorithmData';
import type { Algorithm } from '../data/algorithmData';
import './AlgorithmTrainer.css';

const AlgorithmTrainer: React.FC = () => {
  const { rotate, reset } = useCubeStore();
  const [selectedAlg, setSelectedAlg] = useState<Algorithm | null>(null);

  const parseAndExecute = (sequence: string) => {
    reset();
    const moves = sequence.split(' ');
    moves.forEach(move => {
      const direction = move.includes("'") ? -1 : 1;
      const layerMove = move.replace("'", '').replace('2', '');

      const executeMove = (m: string, dir: number) => {
        switch (m) {
          case 'R': rotate('x', 1, -dir); break;
          case 'L': rotate('x', -1, dir); break;
          case 'U': rotate('y', 1, dir); break;
          case 'D': rotate('y', -1, -dir); break;
          case 'F': rotate('z', 1, dir); break;
          case 'B': rotate('z', -1, -dir); break;
          // Wide and slice moves can be added here
        }
      }

      executeMove(layerMove, direction);
      if (move.includes('2')) {
        executeMove(layerMove, direction);
      }
    });
  };

  return (
    <div className="algorithm-trainer">
      <h2>Algorithm Trainer</h2>
      <div className="alg-selection">
        <select onChange={(e) => setSelectedAlg(JSON.parse(e.target.value))}>
          <option>Select an Algorithm</option>
          <optgroup label="OLL">
            {ollAlgorithms.map((alg: Algorithm) => <option key={alg.name} value={JSON.stringify(alg)}>{alg.name}</option>)}
          </optgroup>
          <optgroup label="PLL">
            {pllAlgorithms.map((alg: Algorithm) => <option key={alg.name} value={JSON.stringify(alg)}>{alg.name}</option>)}
          </optgroup>
        </select>
        <button onClick={() => selectedAlg && parseAndExecute(selectedAlg.sequence)} disabled={!selectedAlg}>
          Execute
        </button>
      </div>
      {selectedAlg && (
        <div className="alg-info">
          <h3>{selectedAlg.name}</h3>
          <code>{selectedAlg.sequence}</code>
        </div>
      )}
    </div>
  );
};

export default AlgorithmTrainer; 