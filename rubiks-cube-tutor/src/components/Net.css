.net {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.net-row {
  display: flex;
  gap: 2px;
}

.net-face {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 2px;
  width: 60px;
  height: 60px;
  background-color: #222;
  border: 1px solid #444;
}

.net-face-placeholder {
  width: 60px;
  height: 60px;
}

.sticker {
  width: 100%;
  height: 100%;
} 