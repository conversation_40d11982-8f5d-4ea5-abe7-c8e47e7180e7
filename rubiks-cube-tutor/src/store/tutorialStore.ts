import { create } from 'zustand';
import type { TutorialStep } from '../data/tutorialData';
import { beginnerMethod } from '../data/tutorialData';
import { cfopMethod } from '../data/cfopMethod';
import { rouxMethod } from '../data/rouxMethod';

const methods = {
  beginner: beginnerMethod,
  cfop: cfopMethod,
  roux: rouxMethod,
};

type Method = 'beginner' | 'cfop' | 'roux';

interface TutorialStore {
  method: Method;
  currentStep: number;
  tutorial: TutorialStep[];
  setMethod: (method: Method) => void;
  nextStep: () => void;
  prevStep: () => void;
}

const useTutorialStore = create<TutorialStore>((set, get) => ({
  method: 'beginner',
  currentStep: 0,
  tutorial: methods.beginner,
  setMethod: (method) => {
    set({ method, tutorial: methods[method], currentStep: 0 });
  },
  nextStep: () => {
    const { currentStep, tutorial } = get();
    if (currentStep < tutorial.length - 1) {
      set({ currentStep: currentStep + 1 });
    }
  },
  prevStep: () => {
    const { currentStep } = get();
    if (currentStep > 0) {
      set({ currentStep: currentStep - 1 });
    }
  },
}));

export default useTutorialStore; 