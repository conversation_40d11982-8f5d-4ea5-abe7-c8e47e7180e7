import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const defaultColors = {
  top: 'white',
  bottom: 'yellow',
  front: 'green',
  back: 'blue',
  left: 'orange',
  right: 'red',
};

interface SettingsStore {
  colors: typeof defaultColors;
  setColor: (face: keyof typeof defaultColors, color: string) => void;
  resetColors: () => void;
}

const useSettingsStore = create(
  persist<SettingsStore>(
    (set) => ({
      colors: defaultColors,
      setColor: (face, color) =>
        set((state) => ({
          colors: { ...state.colors, [face]: color },
        })),
      resetColors: () => set({ colors: defaultColors }),
    }),
    {
      name: 'rubiks-cube-settings',
    }
  )
);

export default useSettingsStore; 