import { create } from 'zustand';
import * as THREE from 'three';
import { initialCubieStickers, faceNormals } from '../utils/cubeUtils';
import useSettingsStore from '../store/settingsStore';
import type { TutorialStep } from '../data/tutorialData';

interface CubieState {
  id: string;
  position: THREE.Vector3;
  quaternion: THREE.Quaternion;
}

type Rotation = {
  axis: 'x' | 'y' | 'z';
  layer: number;
  direction: number;
};

interface StickerState {
  id: string;
  cubieId: string;
  face: keyof typeof faceNormals;
  color: string;
}

interface CubeStore {
  cubies: CubieState[];
  stickers: StickerState[];
  isAnimating: boolean;
  rotationQueue: Rotation[];
  highlightedCubies: string[];
  init: () => void;
  rotate: (axis: 'x' | 'y' | 'z', layer: number, direction: number) => void;
  onAnimationComplete: () => void;
  scramble: () => void;
  reset: () => void;
  setHighlightedCubies: (cubieIds: string[]) => void;
  getHint: (step: TutorialStep) => string;
}

const useCubeStore = create<CubeStore>((set, get) => ({
  cubies: [],
  stickers: [],
  isAnimating: false,
  rotationQueue: [],
  highlightedCubies: [],
  init: () => {
    const cubies: CubieState[] = [];
    for (let x = -1; x <= 1; x++) {
      for (let y = -1; y <= 1; y++) {
        for (let z = -1; z <= 1; z++) {
          if (x === 0 && y === 0 && z === 0) continue;
          cubies.push({
            id: `${x}-${y}-${z}`,
            position: new THREE.Vector3(x, y, z),
            quaternion: new THREE.Quaternion(),
          });
        }
      }
    }
    const stickers = initialCubieStickers().map((s, i) => ({ ...s, id: `sticker-${i}` }));
    set({ cubies, stickers, isAnimating: false, rotationQueue: [], highlightedCubies: [] });
  },
  rotate: (axis, layer, direction) => {
    set((state) => ({
      rotationQueue: [...state.rotationQueue, { axis, layer, direction }],
    }));

    if (!get().isAnimating) {
      get().onAnimationComplete();
    }
  },
  onAnimationComplete: () => {
    const { rotationQueue } = get();
    if (rotationQueue.length === 0) {
      set({ isAnimating: false });
      return;
    }

    set({ isAnimating: true });
    const { axis, layer, direction } = rotationQueue[0];

    const rotation = new THREE.Quaternion().setFromAxisAngle(
      new THREE.Vector3(
        axis === 'x' ? 1 : 0,
        axis === 'y' ? 1 : 0,
        axis === 'z' ? 1 : 0
      ),
      (Math.PI / 2) * direction
    );

    const updatedCubies = get().cubies.map((cubie) => {
      if (Math.round(cubie.position[axis]) === layer) {
        const newPosition = cubie.position.clone().applyQuaternion(rotation);
        const newQuaternion = rotation.clone().multiply(cubie.quaternion);
        return { ...cubie, position: newPosition, quaternion: newQuaternion };
      }
      return cubie;
    });

    const updatedStickers = get().stickers.map(sticker => {
      const cubie = get().cubies.find(c => c.id === sticker.cubieId);
      if (cubie && Math.round(cubie.position[axis]) === layer) {
        const newNormal = faceNormals[sticker.face].clone().applyQuaternion(rotation);

        let newFace: keyof typeof faceNormals = 'front';
        let minAngle = Infinity;

        for (const [face, normal] of Object.entries(faceNormals)) {
          const angle = newNormal.angleTo(normal);
          if (angle < minAngle) {
            minAngle = angle;
            newFace = face as keyof typeof faceNormals;
          }
        }
        
        const newCubie = updatedCubies.find(c =>
            c.position.distanceTo(cubie.position.clone().applyQuaternion(rotation)) < 0.1
        );

        return {
          ...sticker,
          cubieId: newCubie ? newCubie.id : sticker.cubieId,
          face: newFace,
        };
      }
      return sticker;
    });


    set({
      cubies: updatedCubies,
      stickers: updatedStickers,
      rotationQueue: get().rotationQueue.slice(1),
    });
  },
  scramble: () => {
    const moves: Rotation[] = [];
    const axes: ('x' | 'y' | 'z')[] = ['x', 'y', 'z'];
    const layers = [-1, 1];
    const directions = [-1, 1];

    for (let i = 0; i < 25; i++) {
      const axis = axes[Math.floor(Math.random() * axes.length)];
      const layer = layers[Math.floor(Math.random() * layers.length)];
      const direction = directions[Math.floor(Math.random() * directions.length)];
      moves.push({ axis, layer, direction });
    }

    set((state) => ({
      rotationQueue: [...state.rotationQueue, ...moves],
    }));

    if (!get().isAnimating) {
      get().onAnimationComplete();
    }
  },
  reset: () => {
    get().init();
  },
  setHighlightedCubies: (cubieIds) => {
    set({ highlightedCubies: cubieIds });
  },
  getHint: (step) => {
    const { cubies, stickers, setHighlightedCubies } = get();
    const { colors } = useSettingsStore.getState();

    if (step.title === 'Step 1: The White Cross') {
      // Find white edge stickers
      const whiteEdgeStickers = stickers.filter(sticker =>
        sticker.color === colors.top &&
        cubies.find(c => c.id === sticker.cubieId)?.position.manhattanLength() === 1
      );

      for (const whiteSticker of whiteEdgeStickers) {
        const cubie = cubies.find(c => c.id === whiteSticker.cubieId);
        if (!cubie) continue;

        const otherSticker = stickers.find(s => s.cubieId === cubie.id && s.id !== whiteSticker.id);
        if (!otherSticker) continue;

        // The face of the center that should match the otherSticker's color
        const targetFaceNormal = faceNormals[otherSticker.face].clone().applyQuaternion(cubie.quaternion).round();

        // Find the center cubie of that face
        const centerCubieId = `${targetFaceNormal.x}-${targetFaceNormal.y}-${targetFaceNormal.z}`;
        const centerSticker = stickers.find(s => s.cubieId === centerCubieId);

        // Check if the white sticker is on the top face (y=1)
        const whiteStickerOnTop = Math.round(faceNormals[whiteSticker.face].clone().applyQuaternion(cubie.quaternion).y) === 1;

        if (!centerSticker || !whiteStickerOnTop || otherSticker.color !== centerSticker.color) {
          setHighlightedCubies([cubie.id]);
          return `This white edge piece is not solved correctly. Try to move it to the top face with the white sticker facing up, and the other color matching the center piece.`;
        }
      }
      
      setHighlightedCubies([]); // No hint found
      return 'All white cross pieces are solved correctly!';
    } else if (step.title === 'Step 2: Solve the White Corners') {
      const whiteCornerStickers = stickers.filter(sticker =>
        sticker.color === colors.top &&
        cubies.find(c => c.id === sticker.cubieId)?.position.manhattanLength() === 3
      );

      for (const whiteSticker of whiteCornerStickers) {
        const cubie = cubies.find(c => c.id === whiteSticker.cubieId);
        if (!cubie) continue;

        if (Math.round(cubie.position.y) !== 1) {
          setHighlightedCubies([cubie.id]);
          return `This white corner piece is not in the top layer. Try to move it to the top layer.`;
        }

        // Check if the corner is in the correct position
        const otherStickers = stickers.filter(s => s.cubieId === cubie.id && s.id !== whiteSticker.id);
        const isCorrectlyPositioned = otherStickers.every(otherSticker => {
          const targetFaceNormal = faceNormals[otherSticker.face].clone().applyQuaternion(cubie.quaternion).round();
          const centerCubieId = `${targetFaceNormal.x}-${targetFaceNormal.y}-${targetFaceNormal.z}`;
          const centerSticker = stickers.find(s => s.cubieId === centerCubieId);
          return centerSticker && otherSticker.color === centerSticker.color;
        });

        if (!isCorrectlyPositioned) {
          setHighlightedCubies([cubie.id]);
          return `This white corner piece is not in the correct position. The side colors do not match the center pieces.`;
        }
      }

      setHighlightedCubies([]);
      return 'All white corners are solved correctly!';
    } else if (step.title === 'Step 3: Solve the Middle Layer') {
      const yellowColor = colors.bottom;
      const middleLayerEdges = cubies.filter(c => c.position.manhattanLength() === 2 && c.position.y === 0);

      for (const edge of middleLayerEdges) {
        const edgeStickers = stickers.filter(s => s.cubieId === edge.id);
        const isCorrectlyPositioned = edgeStickers.every(sticker => {
          const targetFaceNormal = faceNormals[sticker.face].clone().applyQuaternion(edge.quaternion).round();
          const centerCubieId = `${targetFaceNormal.x}-${targetFaceNormal.y}-${targetFaceNormal.z}`;
          const centerSticker = stickers.find(s => s.cubieId === centerCubieId);
          return centerSticker && sticker.color === centerSticker.color;
        });

        if (!isCorrectlyPositioned) {
          const bottomLayerEdges = cubies.filter(c => c.position.manhattanLength() === 2 && c.position.y === -1);
          for (const bottomEdge of bottomLayerEdges) {
            const bottomEdgeStickers = stickers.filter(s => s.cubieId === bottomEdge.id);
            if (!bottomEdgeStickers.some(s => s.color === yellowColor)) {
              setHighlightedCubies([bottomEdge.id]);
              return `This edge piece belongs in the middle layer. Move it to the top layer, then use the algorithm to place it correctly.`;
            }
          }
          setHighlightedCubies([edge.id]);
          return `This middle layer edge is not solved. You may need to take it out and re-insert it correctly.`;
        }
      }

      setHighlightedCubies([]);
      return 'All middle layer edges are solved correctly!';
    } else if (step.title === 'Step 4: The Yellow Cross') {
      const yellowColor = colors.bottom;
      const topEdges = cubies.filter(c => c.position.manhattanLength() === 2 && c.position.y === 1);
      const yellowEdgeStickersOnTop = topEdges.filter(edge => {
        const topSticker = stickers.find(s => s.cubieId === edge.id && Math.round(faceNormals[s.face].clone().applyQuaternion(edge.quaternion).y) === 1);
        return topSticker && topSticker.color === yellowColor;
      });

      if (yellowEdgeStickersOnTop.length < 4) {
        setHighlightedCubies(topEdges.map(e => e.id));
        return `You don't have a yellow cross yet. Use the algorithm F R U R' U' F' to cycle through the states until you get a cross.`;
      }

      setHighlightedCubies([]);
      return 'You have the yellow cross!';
    } else if (step.title === 'Step 5: Position the Yellow Edges') {
      const topEdges = cubies.filter(c => c.position.manhattanLength() === 2 && c.position.y === 1);
      const unsolvedEdges = topEdges.filter(edge => {
        const sideSticker = stickers.find(s => s.cubieId === edge.id && Math.round(faceNormals[s.face].clone().applyQuaternion(edge.quaternion).y) === 0);
        if (!sideSticker) return true;
        
        const targetFaceNormal = faceNormals[sideSticker.face].clone().applyQuaternion(edge.quaternion).round();
        const centerCubieId = `${targetFaceNormal.x}-${targetFaceNormal.y}-${targetFaceNormal.z}`;
        const centerSticker = stickers.find(s => s.cubieId === centerCubieId);
        return !centerSticker || sideSticker.color !== centerSticker.color;
      });

      if (unsolvedEdges.length > 0) {
        setHighlightedCubies(unsolvedEdges.map(e => e.id));
        return `Not all yellow edges are in the correct position. Use the algorithm R U R' U R U2 R' to swap them.`;
      }

      setHighlightedCubies([]);
      return 'All yellow edges are positioned correctly!';
    } else if (step.title === 'Step 6: Position the Yellow Corners') {
      const topCorners = cubies.filter(c => c.position.manhattanLength() === 3 && c.position.y === 1);
      const unsolvedCorners = topCorners.filter(corner => {
        const sideStickers = stickers.filter(s => s.cubieId === corner.id && Math.round(faceNormals[s.face].clone().applyQuaternion(corner.quaternion).y) === 0);
        return !sideStickers.every(sticker => {
          const targetFaceNormal = faceNormals[sticker.face].clone().applyQuaternion(corner.quaternion).round();
          const centerCubieId = `${targetFaceNormal.x}-${targetFaceNormal.y}-${targetFaceNormal.z}`;
          const centerSticker = stickers.find(s => s.cubieId === centerCubieId);
          return centerSticker && sticker.color === centerSticker.color;
        });
      });

      if (unsolvedCorners.length > 0) {
        setHighlightedCubies(unsolvedCorners.map(c => c.id));
        return `Not all yellow corners are in the correct position. Use the algorithm U R U' L' U R' U' L to cycle them.`;
      }

      setHighlightedCubies([]);
      return 'All yellow corners are positioned correctly!';
    } else if (step.title === 'Step 7: Orient the Yellow Corners') {
      const yellowColor = colors.bottom;
      const topCorners = cubies.filter(c => c.position.manhattanLength() === 3 && c.position.y === 1);
      const unsolvedCorners = topCorners.filter(corner => {
        const topSticker = stickers.find(s => s.cubieId === corner.id && Math.round(faceNormals[s.face].clone().applyQuaternion(corner.quaternion).y) === 1);
        return !topSticker || topSticker.color !== yellowColor;
      });

      if (unsolvedCorners.length > 0) {
        setHighlightedCubies(unsolvedCorners.map(c => c.id));
        return `Not all yellow corners are oriented correctly. Use the algorithm R' D' R D to orient them one by one.`;
      }

      setHighlightedCubies([]);
      return 'Congratulations, you have solved the cube!';
    }
    
    return "No hint available for this step yet.";
  },
}));

export default useCubeStore;
