import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface ProgressStore {
  solveTimes: number[];
  addSolveTime: (time: number) => void;
  completedTutorials: string[];
  addCompletedTutorial: (tutorialId: string) => void;
}

const useProgressStore = create(
  persist<ProgressStore>(
    (set) => ({
      solveTimes: [],
      addSolveTime: (time) =>
        set((state) => ({ solveTimes: [...state.solveTimes, time] })),
      completedTutorials: [],
      addCompletedTutorial: (tutorialId) =>
        set((state) => ({
          completedTutorials: [...state.completedTutorials, tutorialId],
        })),
    }),
    {
      name: 'rubiks-cube-progress',
    }
  )
);

export default useProgressStore; 