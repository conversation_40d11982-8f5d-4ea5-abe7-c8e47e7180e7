import * as THREE from 'three';
import useSettingsStore from '../store/settingsStore';

export const faceNormals = {
  right: new THREE.Vector3(1, 0, 0),
  left: new THREE.Vector3(-1, 0, 0),
  top: new THREE.Vector3(0, 1, 0),
  bottom: new THREE.Vector3(0, -1, 0),
  front: new THREE.Vector3(0, 0, 1),
  back: new THREE.Vector3(0, 0, -1),
};

export const initialCubieStickers = () => {
  const colors = useSettingsStore.getState().colors;
  const stickers: { cubieId: string, face: keyof typeof faceNormals, color: string }[] = [];
  for (let x = -1; x <= 1; x++) {
    for (let y = -1; y <= 1; y++) {
      for (let z = -1; z <= 1; z++) {
        if (x === 0 && y === 0 && z === 0) continue;
        const id = `${x}-${y}-${z}`;

        if (x === 1) stickers.push({ cubieId: id, face: 'right', color: colors.right });
        if (x === -1) stickers.push({ cubieId: id, face: 'left', color: colors.left });
        if (y === 1) stickers.push({ cubieId: id, face: 'top', color: colors.top });
        if (y === -1) stickers.push({ cubieId: id, face: 'bottom', color: colors.bottom });
        if (z === 1) stickers.push({ cubieId: id, face: 'front', color: colors.front });
        if (z === -1) stickers.push({ cubieId: id, face: 'back', color: colors.back });
      }
    }
  }
  return stickers;
}; 