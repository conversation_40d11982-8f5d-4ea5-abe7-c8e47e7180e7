import React, { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import Cube from '../components/Cube';
import Controls from '../components/Controls';
import Net from '../components/Net';
import Timer from '../components/Timer';
import LoadingSpinner from '../components/LoadingSpinner';

const PracticePage: React.FC = () => {
  return (
    <div style={{ height: 'calc(100vh - 80px)', background: '#272727', position: 'relative' }}>
      <Timer />
      <Suspense fallback={<LoadingSpinner message="Loading 3D Cube..." />}>
        <Canvas>
          <ambientLight intensity={0.5} />
          <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} />
          <pointLight position={[-10, -10, -10]} />
          <Cube />
          <OrbitControls />
        </Canvas>
      </Suspense>
      <Controls />
      <Net />
    </div>
  );
};

export default PracticePage; 