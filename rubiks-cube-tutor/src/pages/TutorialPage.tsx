import React from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import Cube from '../components/Cube';
import Net from '../components/Net';
import Tutorial from '../components/Tutorial';
import useTutorialStore from '../store/tutorialStore';
import './TutorialPage.css';

const TutorialPage: React.FC = () => {
  const { method, setMethod } = useTutorialStore();

  return (
    <div style={{ height: 'calc(100vh - 80px)', background: '#272727', position: 'relative' }}>
      <Canvas>
        <ambientLight intensity={0.5} />
        <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} />
        <pointLight position={[-10, -10, -10]} />
        <Cube />
        <OrbitControls />
      </Canvas>
      <Net />
      <div className="tutorial-controls">
        <select onChange={(e) => setMethod(e.target.value as 'beginner' | 'cfop' | 'roux')} value={method}>
          <option value="beginner">Beginner</option>
          <option value="cfop">CFOP</option>
          <option value="roux">Roux</option>
        </select>
      </div>
      <Tutorial />
    </div>
  );
};

export default TutorialPage; 