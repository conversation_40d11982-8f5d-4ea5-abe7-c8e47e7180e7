import React from 'react';
import useSettingsStore from '../store/settingsStore';
import './SettingsPage.css';

const SettingsPage: React.FC = () => {
  const { colors, setColor, resetColors } = useSettingsStore();

  return (
    <div className="settings-page">
      <h1>Settings</h1>
      <div className="settings-section">
        <h2>Cube Colors</h2>
        <div className="color-pickers">
          {Object.entries(colors).map(([face, color]) => (
            <div key={face} className="color-picker">
              <label>{face.charAt(0).toUpperCase() + face.slice(1)}</label>
              <input
                type="color"
                value={color}
                onChange={(e) => setColor(face as keyof typeof colors, e.target.value)}
              />
            </div>
          ))}
        </div>
        <button onClick={resetColors} className="reset-button">
          Reset to Default
        </button>
      </div>
    </div>
  );
};

export default SettingsPage; 