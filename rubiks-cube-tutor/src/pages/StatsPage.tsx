import React from 'react';
import useProgressStore from '../store/progressStore';
import './StatsPage.css';

const StatsPage: React.FC = () => {
  const { solveTimes, completedTutorials } = useProgressStore();

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60000);
    const seconds = Math.floor((time % 60000) / 1000);
    const milliseconds = Math.floor((time % 1000) / 10);
    return `${minutes.toString().padStart(2, '0')}:${seconds
      .toString()
      .padStart(2, '0')}.${milliseconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="stats-page">
      <h1>Your Progress</h1>
      <div className="stats-section">
        <h2>Solve Times</h2>
        {solveTimes.length > 0 ? (
          <ul>
            {solveTimes.map((time, index) => (
              <li key={index}>{formatTime(time)}</li>
            ))}
          </ul>
        ) : (
          <p>No solves recorded yet.</p>
        )}
      </div>
      <div className="stats-section">
        <h2>Completed Tutorials</h2>
        {completedTutorials.length > 0 ? (
          <ul>
            {completedTutorials.map((tutorial, index) => (
              <li key={index}>{tutorial}</li>
            ))}
          </ul>
        ) : (
          <p>No tutorials completed yet.</p>
        )}
      </div>
    </div>
  );
};

export default StatsPage; 