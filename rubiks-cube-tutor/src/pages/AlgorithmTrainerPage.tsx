import React from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import Cube from '../components/Cube';
import Net from '../components/Net';
import AlgorithmTrainer from '../components/AlgorithmTrainer';

const AlgorithmTrainerPage: React.FC = () => {
  return (
    <div style={{ height: 'calc(100vh - 80px)', background: '#272727', position: 'relative' }}>
      <Canvas>
        <ambientLight intensity={0.5} />
        <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} />
        <pointLight position={[-10, -10, -10]} />
        <Cube />
        <OrbitControls />
      </Canvas>
      <Net />
      <AlgorithmTrainer />
    </div>
  );
};

export default AlgorithmTrainerPage; 