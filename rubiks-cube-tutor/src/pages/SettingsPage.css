.settings-page {
  padding: 20px;
  color: white;
}

.settings-section {
  margin-bottom: 30px;
}

.settings-section h2 {
  border-bottom: 2px solid #444;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.color-pickers {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 20px;
}

.color-picker {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #333;
  padding: 15px;
  border-radius: 8px;
}

.color-picker label {
  margin-bottom: 10px;
  font-weight: bold;
}

.color-picker input[type="color"] {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: transparent;
}

.color-picker input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-picker input[type="color"]::-webkit-color-swatch {
  border-radius: 50%;
  border: 2px solid #555;
}

.reset-button {
  margin-top: 20px;
  background: #f44336;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
}

.reset-button:hover {
  background: #d32f2f;
}
