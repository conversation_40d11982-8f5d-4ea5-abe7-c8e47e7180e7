# Rubik's Cube Tutor

A comprehensive interactive Rubik's Cube learning application built with React, TypeScript, and Three.js. This application serves as a complete tool for both beginners and experienced cubers to learn, practice, and improve their solving skills.

## Features

### 🎯 Core Features
- **Interactive 3D Cube**: Fully functional 3D Rubik's Cube with realistic physics and animations
- **Multiple Solving Methods**: Step-by-step tutorials for beginner's method, CFOP, and Roux
- **Algorithm Trainer**: Practice specific algorithms with guided instructions
- **Timer**: Built-in speedcubing timer with statistics tracking
- **Progress Tracking**: Monitor your improvement over time with detailed statistics
- **Hint System**: Get contextual hints based on the current cube state

### 🎨 User Experience
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Touch Controls**: Intuitive touch gestures for mobile users
- **Customization**: Personalize cube colors and themes
- **2D Net View**: Visual representation of the cube's current state
- **Highlighting System**: Visual cues to help identify relevant pieces during tutorials

### 🛠 Technical Features
- **Modern Tech Stack**: Built with React 19, TypeScript, and Three.js
- **State Management**: Efficient state management with Zustand
- **3D Graphics**: Powered by react-three-fiber and @react-three/drei
- **Smooth Animations**: Fluid cube rotations with react-spring
- **Code Quality**: ESLint configuration with TypeScript support

## Getting Started

### Prerequisites
- Node.js (version 18 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd rubiks-cube-tutor
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm run preview` - Preview the production build
- `npm run lint` - Run ESLint to check code quality

## Usage

### Navigation
- **Home**: Welcome page with overview
- **Practice**: Free-play mode with timer and controls
- **Tutorial**: Step-by-step solving guides
- **Trainer**: Algorithm practice mode
- **Stats**: View your progress and statistics
- **Settings**: Customize colors and preferences

### Controls
- **Mouse/Touch**: Click and drag on cube faces to rotate layers
- **Buttons**: Use the control panel for specific moves
- **Scramble**: Generate random cube states
- **Reset**: Return to solved state
- **Timer**: Start/stop timing your solves

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Cube.tsx        # Main 3D cube component
│   ├── Cubie.tsx       # Individual cube piece
│   ├── Controls.tsx    # Control panel
│   ├── Timer.tsx       # Speedcubing timer
│   └── ...
├── pages/              # Page components
│   ├── HomePage.tsx    # Landing page
│   ├── PracticePage.tsx # Practice mode
│   ├── TutorialPage.tsx # Tutorial system
│   └── ...
├── store/              # State management
│   ├── cubeStore.ts    # Cube state and logic
│   ├── settingsStore.ts # User preferences
│   └── ...
├── data/               # Static data and algorithms
├── utils/              # Utility functions
└── styles/             # CSS styles
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Technologies Used

- **React 19** - UI framework
- **TypeScript** - Type safety
- **Three.js** - 3D graphics
- **react-three-fiber** - React renderer for Three.js
- **@react-three/drei** - Useful helpers for react-three-fiber
- **Zustand** - State management
- **react-spring** - Animation library
- **Vite** - Build tool and development server

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Inspired by the speedcubing community
- Built with modern web technologies
- Designed for educational purposes
