import { GestureKey, EngineClass, Action } from "./types.js";
import { ResolverMap } from "./config/resolver.js";
export declare const EngineMap: Map<GestureKey, EngineClass<any>>;
export declare const ConfigResolverMap: Map<GestureKey, ResolverMap>;
export declare function registerAction(action: Action): void;
export declare const dragAction: Action;
export declare const hoverAction: Action;
export declare const moveAction: Action;
export declare const pinchAction: Action;
export declare const scrollAction: Action;
export declare const wheelAction: Action;
